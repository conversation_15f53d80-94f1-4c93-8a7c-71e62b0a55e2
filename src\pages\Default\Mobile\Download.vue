<script setup>
import { ref } from 'vue';

const downloadProgress = ref(0);
const isDownloading = ref(false);

const startDownload = () => {
  isDownloading.value = true;
  downloadProgress.value = 0;
  
  // 模拟下载进度
  const interval = setInterval(() => {
    downloadProgress.value += 10;
    if (downloadProgress.value >= 100) {
      clearInterval(interval);
      isDownloading.value = false;
    }
  }, 200);
};
</script>

<template>
  <div class="download-mobile">
    <h1>下载页面 - 移动端</h1>
    <div class="download-content">
      <p>这是移动端的下载页面</p>
      
      <div class="download-section">
        <van-button 
          type="primary" 
          @click="startDownload"
          :disabled="isDownloading"
          class="download-btn"
        >
          {{ isDownloading ? '下载中...' : '开始下载' }}
        </van-button>
        
        <div v-if="isDownloading" class="progress-section">
          <van-progress 
            :percentage="downloadProgress" 
            stroke-width="8"
            color="#1989fa"
          />
          <p class="progress-text">{{ downloadProgress }}%</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.download-mobile {
  padding: 20px;
  min-height: 100vh;
  background-color: #f8f9fa;
}

.download-mobile h1 {
  color: #1989fa;
  text-align: center;
  margin-bottom: 30px;
}

.download-content {
  max-width: 400px;
  margin: 0 auto;
}

.download-content p {
  color: #666;
  font-size: 16px;
  text-align: center;
  margin-bottom: 30px;
}

.download-section {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.download-btn {
  width: 100%;
  height: 44px;
  border-radius: 22px;
  margin-bottom: 20px;
}

.progress-section {
  margin-top: 20px;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}
</style>
