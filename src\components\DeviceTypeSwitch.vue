<script setup>
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

// 获取当前设备类型
const currentDeviceType = computed(() => {
  return route.meta?.deviceType || 'mobile';
});

// 获取当前页面名称
const currentPageName = computed(() => {
  const path = route.path;
  if (path.includes('/home/')) return 'home';
  if (path.includes('/share/')) return 'share';
  if (path.includes('/download/')) return 'download';
  return 'home';
});

// 切换设备类型
const switchDeviceType = (deviceType) => {
  const pageName = currentPageName.value;
  router.push(`/${pageName}/${deviceType}`);
};

// 检查是否为当前设备类型
const isCurrentDevice = (deviceType) => {
  return currentDeviceType.value === deviceType;
};
</script>

<template>
  <div class="device-switch">
    <div class="switch-label">设备类型:</div>
    <div class="switch-buttons">
      <button 
        @click="switchDeviceType('mobile')"
        :class="['switch-btn', { active: isCurrentDevice('mobile') }]"
      >
        📱 移动端
      </button>
      <button 
        @click="switchDeviceType('pc')"
        :class="['switch-btn', { active: isCurrentDevice('pc') }]"
      >
        💻 PC端
      </button>
    </div>
  </div>
</template>

<style scoped>
.device-switch {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 10px 15px;
  border-radius: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.switch-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.switch-buttons {
  display: flex;
  gap: 5px;
}

.switch-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 15px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  color: #666;
}

.switch-btn:hover {
  background: rgba(116, 185, 255, 0.1);
  color: #74b9ff;
}

.switch-btn.active {
  background: #74b9ff;
  color: white;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .device-switch {
    top: 10px;
    right: 10px;
    padding: 8px 12px;
  }
  
  .switch-label {
    display: none;
  }
  
  .switch-btn {
    padding: 5px 10px;
    font-size: 11px;
  }
}

/* 暗色模式 */
@media (prefers-color-scheme: dark) {
  .device-switch {
    background: rgba(28, 28, 30, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .switch-label {
    color: #aaa;
  }
  
  .switch-btn {
    color: #aaa;
  }
  
  .switch-btn:hover {
    background: rgba(116, 185, 255, 0.2);
    color: #74b9ff;
  }
}
</style>
