import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router';

// 导入Vant组件和样式
import { Button, Loading, Image as VanImage, Toast, Dialog } from 'vant';
import 'vant/lib/index.css';
// 导入移动端适配
import 'amfe-flexible';

const app = createApp(App);

// 注册Vant组件
app.use(Button);
app.use(Loading);
app.use(VanImage);
app.use(Toast);
app.use(Dialog);

app.use(router)
   .mount('#app'); 
