<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>路由测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .route-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #74b9ff;
        }
        .route-section h2 {
            color: #2d3436;
            margin-bottom: 15px;
        }
        .route-list {
            list-style: none;
            padding: 0;
        }
        .route-list li {
            margin-bottom: 10px;
            padding: 10px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .route-link {
            color: #74b9ff;
            text-decoration: none;
            font-weight: 500;
        }
        .route-link:hover {
            text-decoration: underline;
        }
        .description {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
        }
        .device-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .device-info h3 {
            margin: 0 0 10px 0;
            color: #0984e3;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        .status.auto {
            background: #00b894;
            color: white;
        }
        .status.manual {
            background: #fdcb6e;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 设备类型路由系统测试</h1>
        
        <div class="device-info">
            <h3>当前设备信息</h3>
            <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
            <p><strong>屏幕尺寸:</strong> <span id="screenSize"></span></p>
            <p><strong>检测结果:</strong> <span id="deviceType"></span></p>
        </div>

        <div class="route-section">
            <h2>🔄 自动检测路由 <span class="status auto">自动重定向</span></h2>
            <ul class="route-list">
                <li>
                    <a href="/" class="route-link">/ (根路径)</a>
                    <div class="description">自动检测设备类型并重定向到对应首页</div>
                </li>
                <li>
                    <a href="/home" class="route-link">/home</a>
                    <div class="description">自动检测设备类型并重定向到对应首页</div>
                </li>
                <li>
                    <a href="/share" class="route-link">/share</a>
                    <div class="description">自动检测设备类型并重定向到对应分享页</div>
                </li>
                <li>
                    <a href="/download" class="route-link">/download</a>
                    <div class="description">自动检测设备类型并重定向到对应下载页</div>
                </li>
            </ul>
        </div>

        <div class="route-section">
            <h2>📱 移动端路由 <span class="status manual">手动指定</span></h2>
            <ul class="route-list">
                <li>
                    <a href="/home/<USER>" class="route-link">/home/<USER>/a>
                    <div class="description">移动端首页 - 简洁的移动端界面</div>
                </li>
                <li>
                    <a href="/share/mobile" class="route-link">/share/mobile</a>
                    <div class="description">移动端分享页 - 设备分享功能</div>
                </li>
                <li>
                    <a href="/download/mobile" class="route-link">/download/mobile</a>
                    <div class="description">移动端下载页 - 下载进度显示</div>
                </li>
            </ul>
        </div>

        <div class="route-section">
            <h2>💻 PC端路由 <span class="status manual">手动指定</span></h2>
            <ul class="route-list">
                <li>
                    <a href="/home/<USER>" class="route-link">/home/<USER>/a>
                    <div class="description">PC端首页 - 功能丰富的桌面界面</div>
                </li>
                <li>
                    <a href="/share/pc" class="route-link">/share/pc</a>
                    <div class="description">PC端分享页 - 完整的分享管理界面</div>
                </li>
                <li>
                    <a href="/download/pc" class="route-link">/download/pc</a>
                    <div class="description">PC端下载页 - 专业的下载管理器</div>
                </li>
            </ul>
        </div>

        <div class="route-section">
            <h2>✨ 功能特性</h2>
            <ul class="route-list">
                <li>
                    <strong>自动设备检测:</strong> 根据User-Agent和屏幕尺寸自动判断设备类型
                </li>
                <li>
                    <strong>组件懒加载:</strong> 只加载当前需要的组件，提升性能
                </li>
                <li>
                    <strong>设备切换:</strong> 右上角提供设备类型切换按钮
                </li>
                <li>
                    <strong>响应式设计:</strong> 每个设备类型都有专门优化的界面
                </li>
                <li>
                    <strong>扩展性:</strong> 预留应用类型扩展接口，便于后续添加新应用
                </li>
            </ul>
        </div>
    </div>

    <script>
        // 设备检测函数（与路由中的逻辑一致）
        function detectDeviceType() {
            const userAgent = navigator.userAgent.toLowerCase();
            const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
            const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent);
            const screenWidth = window.innerWidth;
            
            if (isMobile && !isTablet) {
                return 'mobile';
            } else if (screenWidth < 768) {
                return 'mobile';
            } else {
                return 'pc';
            }
        }

        // 显示设备信息
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('screenSize').textContent = `${window.innerWidth} x ${window.innerHeight}`;
        document.getElementById('deviceType').textContent = detectDeviceType();
        
        // 更新设备信息（窗口大小改变时）
        window.addEventListener('resize', () => {
            document.getElementById('screenSize').textContent = `${window.innerWidth} x ${window.innerHeight}`;
            document.getElementById('deviceType').textContent = detectDeviceType();
        });
    </script>
</body>
</html>
