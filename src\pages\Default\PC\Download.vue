<script setup>
import { ref, reactive } from 'vue';

const downloads = ref([
  { id: 1, name: '文档.pdf', size: '2.5MB', progress: 100, status: 'completed' },
  { id: 2, name: '视频.mp4', size: '150MB', progress: 65, status: 'downloading' },
  { id: 3, name: '图片.jpg', size: '1.2MB', progress: 0, status: 'pending' },
  { id: 4, name: '音频.mp3', size: '5.8MB', progress: 30, status: 'downloading' }
]);

const newDownload = reactive({
  url: '',
  name: ''
});

const stats = reactive({
  total: 4,
  completed: 1,
  downloading: 2,
  pending: 1
});

const addDownload = () => {
  if (!newDownload.url || !newDownload.name) {
    alert('请填写完整信息');
    return;
  }
  
  const newItem = {
    id: Date.now(),
    name: newDownload.name,
    size: '未知',
    progress: 0,
    status: 'pending'
  };
  
  downloads.value.push(newItem);
  stats.total++;
  stats.pending++;
  
  // 重置表单
  newDownload.url = '';
  newDownload.name = '';
  
  // 模拟开始下载
  setTimeout(() => {
    startDownload(newItem.id);
  }, 1000);
};

const startDownload = (id) => {
  const item = downloads.value.find(d => d.id === id);
  if (!item || item.status !== 'pending') return;
  
  item.status = 'downloading';
  stats.pending--;
  stats.downloading++;
  
  // 模拟下载进度
  const interval = setInterval(() => {
    item.progress += Math.random() * 10;
    if (item.progress >= 100) {
      item.progress = 100;
      item.status = 'completed';
      stats.downloading--;
      stats.completed++;
      clearInterval(interval);
    }
  }, 500);
};

const pauseDownload = (id) => {
  const item = downloads.value.find(d => d.id === id);
  if (item) {
    item.status = 'paused';
  }
};

const removeDownload = (id) => {
  const index = downloads.value.findIndex(d => d.id === id);
  if (index > -1) {
    const item = downloads.value[index];
    downloads.value.splice(index, 1);
    stats.total--;
    stats[item.status]--;
  }
};

const getStatusColor = (status) => {
  switch (status) {
    case 'completed': return '#00b894';
    case 'downloading': return '#74b9ff';
    case 'paused': return '#fdcb6e';
    case 'pending': return '#636e72';
    default: return '#636e72';
  }
};

const getStatusText = (status) => {
  switch (status) {
    case 'completed': return '已完成';
    case 'downloading': return '下载中';
    case 'paused': return '已暂停';
    case 'pending': return '等待中';
    default: return '未知';
  }
};
</script>

<template>
  <div class="download-pc">
    <div class="container">
      <header class="header">
        <h1>下载管理 - PC端</h1>
        <p class="subtitle">管理您的下载任务</p>
      </header>
      
      <!-- 统计面板 -->
      <div class="stats-panel">
        <div class="stat-card">
          <div class="stat-number">{{ stats.total }}</div>
          <div class="stat-label">总任务</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ stats.completed }}</div>
          <div class="stat-label">已完成</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ stats.downloading }}</div>
          <div class="stat-label">下载中</div>
        </div>
        <div class="stat-card">
          <div class="stat-number">{{ stats.pending }}</div>
          <div class="stat-label">等待中</div>
        </div>
      </div>
      
      <!-- 添加下载 -->
      <div class="add-download-panel">
        <h2>添加新下载</h2>
        <div class="add-form">
          <input 
            v-model="newDownload.url"
            type="text" 
            placeholder="下载链接"
            class="form-input"
          />
          <input 
            v-model="newDownload.name"
            type="text" 
            placeholder="文件名"
            class="form-input"
          />
          <button @click="addDownload" class="btn btn-primary">
            添加下载
          </button>
        </div>
      </div>
      
      <!-- 下载列表 -->
      <div class="downloads-panel">
        <h2>下载列表</h2>
        <div class="downloads-list">
          <div 
            v-for="download in downloads" 
            :key="download.id"
            class="download-item"
          >
            <div class="download-info">
              <div class="download-name">{{ download.name }}</div>
              <div class="download-size">{{ download.size }}</div>
            </div>
            
            <div class="download-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill"
                  :style="{ 
                    width: download.progress + '%',
                    backgroundColor: getStatusColor(download.status)
                  }"
                ></div>
              </div>
              <div class="progress-text">{{ Math.round(download.progress) }}%</div>
            </div>
            
            <div class="download-status">
              <span 
                class="status-badge"
                :style="{ backgroundColor: getStatusColor(download.status) }"
              >
                {{ getStatusText(download.status) }}
              </span>
            </div>
            
            <div class="download-actions">
              <button 
                v-if="download.status === 'pending'"
                @click="startDownload(download.id)"
                class="btn btn-small btn-start"
              >
                开始
              </button>
              <button 
                v-if="download.status === 'downloading'"
                @click="pauseDownload(download.id)"
                class="btn btn-small btn-pause"
              >
                暂停
              </button>
              <button 
                @click="removeDownload(download.id)"
                class="btn btn-small btn-remove"
              >
                删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.download-pc {
  min-height: 100vh;
  background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
  padding: 40px 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  color: white;
  margin-bottom: 40px;
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 300;
}

.subtitle {
  font-size: 1.1rem;
  opacity: 0.9;
}

.stats-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 25px;
  text-align: center;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.9;
}

.add-download-panel,
.downloads-panel {
  background: white;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.add-download-panel h2,
.downloads-panel h2 {
  color: #2d3436;
  margin-bottom: 20px;
  font-size: 1.5rem;
  font-weight: 500;
}

.add-form {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 15px;
  align-items: center;
}

.form-input {
  padding: 12px 16px;
  border: 2px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
}

.form-input:focus {
  outline: none;
  border-color: #a29bfe;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #a29bfe;
  color: white;
}

.btn-primary:hover {
  background: #6c5ce7;
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-start {
  background: #00b894;
  color: white;
}

.btn-pause {
  background: #fdcb6e;
  color: white;
}

.btn-remove {
  background: #e17055;
  color: white;
}

.downloads-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.download-item {
  display: grid;
  grid-template-columns: 2fr 3fr 1fr 1fr;
  gap: 20px;
  align-items: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.download-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.download-name {
  font-weight: 500;
  color: #2d3436;
}

.download-size {
  font-size: 12px;
  color: #636e72;
}

.download-progress {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-bar {
  flex: 1;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #636e72;
  min-width: 40px;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: 500;
}

.download-actions {
  display: flex;
  gap: 8px;
}

@media (max-width: 768px) {
  .add-form {
    grid-template-columns: 1fr;
  }
  
  .download-item {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .download-progress {
    order: 3;
  }
  
  .download-actions {
    order: 4;
    justify-content: center;
  }
}
</style>
