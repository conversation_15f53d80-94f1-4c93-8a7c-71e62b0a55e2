import { createRouter, createWebHashHistory } from 'vue-router';
import { defineAsyncComponent } from 'vue';

// 设备类型常量
const DEVICE_TYPES = {
  MOBILE: 'mobile',
  PC: 'pc'
};

// 应用类型常量（预留扩展）
const APP_TYPES = {
  DEFAULT: 'Default'
};

// 设备类型检测函数
const detectDeviceType = () => {
  const userAgent = navigator.userAgent.toLowerCase();
  const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
  const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent);

  // 也可以通过屏幕尺寸判断
  const screenWidth = window.innerWidth;

  if (isMobile && !isTablet) {
    return DEVICE_TYPES.MOBILE;
  } else if (screenWidth < 768) {
    return DEVICE_TYPES.MOBILE;
  } else {
    return DEVICE_TYPES.PC;
  }
};

// Vue版本的组件加载器
const componentLoader = (deviceType = DEVICE_TYPES.MOBILE, componentName = '', appType = APP_TYPES.DEFAULT) => {
  // 根据设备类型动态加载组件
  const basePath = `../pages/${appType}`;

  switch (deviceType) {
    case DEVICE_TYPES.MOBILE:
      return defineAsyncComponent(() => import(`${basePath}/Mobile/${componentName}.vue`));
    case DEVICE_TYPES.PC:
      return defineAsyncComponent(() => import(`${basePath}/PC/${componentName}.vue`));
    default:
      // 默认返回mobile版本
      return defineAsyncComponent(() => import(`${basePath}/Mobile/${componentName}.vue`));
  }
};

// 路由配置
const routes = [
  // 默认首页路由 - 自动检测设备类型
  {
    path: '/',
    name: 'Home',
    redirect: () => {
      const deviceType = detectDeviceType();
      return `/home/<USER>
    }
  },

  // 自动检测设备类型的路由
  {
    path: '/home',
    name: 'HomeAuto',
    redirect: () => {
      const deviceType = detectDeviceType();
      return `/home/<USER>
    }
  },
  {
    path: '/share',
    name: 'ShareAuto',
    redirect: () => {
      const deviceType = detectDeviceType();
      return `/share/${deviceType}`;
    }
  },
  {
    path: '/download',
    name: 'DownloadAuto',
    redirect: () => {
      const deviceType = detectDeviceType();
      return `/download/${deviceType}`;
    }
  },

  // Mobile端路由
  {
    path: '/home/<USER>',
    name: 'HomeMobile',
    component: componentLoader(DEVICE_TYPES.MOBILE, 'Home'),
    meta: {
      title: '首页 - 移动端',
      deviceType: DEVICE_TYPES.MOBILE
    }
  },
  {
    path: '/share/mobile',
    name: 'ShareMobile',
    component: componentLoader(DEVICE_TYPES.MOBILE, 'Share'),
    meta: {
      title: '设备分享 - 移动端',
      deviceType: DEVICE_TYPES.MOBILE
    }
  },
  {
    path: '/download/mobile',
    name: 'DownloadMobile',
    component: componentLoader(DEVICE_TYPES.MOBILE, 'Download'),
    meta: {
      title: '下载 - 移动端',
      deviceType: DEVICE_TYPES.MOBILE
    }
  },

  // PC端路由
  {
    path: '/home/<USER>',
    name: 'HomePC',
    component: componentLoader(DEVICE_TYPES.PC, 'Home'),
    meta: {
      title: '首页 - PC端',
      deviceType: DEVICE_TYPES.PC
    }
  },
  {
    path: '/share/pc',
    name: 'SharePC',
    component: componentLoader(DEVICE_TYPES.PC, 'Share'),
    meta: {
      title: '设备分享 - PC端',
      deviceType: DEVICE_TYPES.PC
    }
  },
  {
    path: '/download/pc',
    name: 'DownloadPC',
    component: componentLoader(DEVICE_TYPES.PC, 'Download'),
    meta: {
      title: '下载 - PC端',
      deviceType: DEVICE_TYPES.PC
    }
  }
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 全局路由守卫
router.beforeEach((to, _from, next) => {
  document.title = to.meta?.title || '默认标题';
  next();
});

export default router;