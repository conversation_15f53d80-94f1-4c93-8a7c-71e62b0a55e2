import { createRouter, createWebHashHistory } from 'vue-router';
import Home from '../pages/Home.vue';
import SharePage from '../pages/Share/SharePage.vue';

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页'
    }
  },
  {
    path: '/share',
    name: 'Share',
    component: SharePage,
    meta: {
      title: '设备分享'
    }
  }
    ,{
    path: '/download',
    name: 'Download',
    component: SharePage,
    meta: {
      title: '设备分享'
    }
  }
];

const router = createRouter({
  history: createWebHashHistory(), // 使用历史模式（去掉URL中的#）
  routes,
});

// 全局路由守卫（可选）
router.beforeEach((to, from, next) => {
  document.title = to.meta.title || '默认标题'; // 动态修改标题
  next();
});

export default router;