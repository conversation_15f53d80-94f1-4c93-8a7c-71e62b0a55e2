<script setup>
import { ref } from 'vue';

const features = ref([
  { title: '功能一', description: '这是PC端的功能描述一', icon: '📊' },
  { title: '功能二', description: '这是PC端的功能描述二', icon: '📈' },
  { title: '功能三', description: '这是PC端的功能描述三', icon: '📋' },
  { title: '功能四', description: '这是PC端的功能描述四', icon: '⚙️' }
]);
</script>

<template>
  <div class="home-pc">
    <header class="header">
      <h1>首页 - PC端</h1>
      <p class="subtitle">欢迎使用PC端管理界面</p>
    </header>
    
    <main class="main-content">
      <div class="features-grid">
        <div 
          v-for="feature in features" 
          :key="feature.title"
          class="feature-card"
        >
          <div class="feature-icon">{{ feature.icon }}</div>
          <h3 class="feature-title">{{ feature.title }}</h3>
          <p class="feature-description">{{ feature.description }}</p>
        </div>
      </div>
      
      <div class="stats-section">
        <div class="stat-item">
          <div class="stat-number">1,234</div>
          <div class="stat-label">总用户数</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">567</div>
          <div class="stat-label">活跃用户</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">89</div>
          <div class="stat-label">在线设备</div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped>
.home-pc {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
}

.header {
  text-align: center;
  margin-bottom: 50px;
}

.header h1 {
  font-size: 3rem;
  margin-bottom: 10px;
  font-weight: 300;
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
}

.main-content {
  max-width: 1200px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}

.feature-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 30px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.feature-icon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.feature-title {
  font-size: 1.5rem;
  margin-bottom: 15px;
  font-weight: 500;
}

.feature-description {
  opacity: 0.9;
  line-height: 1.6;
}

.stats-section {
  display: flex;
  justify-content: center;
  gap: 60px;
  flex-wrap: wrap;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 10px;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 1.1rem;
  opacity: 0.8;
}

@media (max-width: 768px) {
  .home-pc {
    padding: 20px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-section {
    gap: 30px;
  }
  
  .stat-number {
    font-size: 2rem;
  }
}
</style>
